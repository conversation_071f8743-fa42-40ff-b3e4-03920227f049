# 📋 天玑GPU调速器 更新日志

## 🚀 v2.5 → v2.6 (2025-05-19 → 2025-06-01)

### ✨ 核心功能新增

#### 🔧 V2驱动优化机制
- ⚡ **智能频率写入**：当连续检测到相同频率时，通过计数器机制减少不必要的写入操作
- 🔄 **强制写入阈值**：默认设置为5次，当相同频率计数达到阈值时强制执行写入，确保系统稳定性

#### 🚀 启动系统重构
- 🌐 **双语支持增强**：初始化脚本新增完整的中英文双语支持系统
- 📝 **智能日志函数**：新增双语日志，根据系统语言自动选择中文或英文输出
- 🔄 **动态模块描述**：实时更新模块描述状态（启动中→运行中→错误），提供更好的用户反馈
- 🆔 **PID管理机制**：新增进程ID管理，防止重复启动
- 📊 **状态描述系统**：支持运行、停止、错误、启动中等状态显示
- 🔧 **模块描述更新**：动态更新模块信息

### 🔄 功能变更与优化

#### 🎮 交互控制简化
- ❌ **移除手动游戏模式切换**：`action.sh` 脚本不再支持手动开启/关闭游戏模式功能
- 🎯 **菜单精简**：主菜单从4个选项减少为3个（调速器服务控制、日志等级设置、退出）
- 🤖 **专注自动检测**：游戏模式完全依赖自动检测 `games.conf` 中的应用包名

#### 📚 文档系统扩展
- 🌍 **完整英文文档**：新增 `docs/en/README.md` 提供完整的英文版本说明
- 🔗 **社区链接集成**：在文档顶部添加社区徽章
- 📖 **结构化重组**：按功能类型重新组织文档内容，提升可读性
- 🎯 **功能分类优化**：将特性按照"核心功能"、"用户界面与交互"、"技术特性"进行分类

### 🛠️ 技术改进

#### 💻 Rust核心代码优化
- 🎯 **V2驱动写入逻辑**：新增智能频率写入逻辑
- 📊 **计数器机制**：当检测到相同频率时增加计数器，达到阈值时强制写入
- 🔄 **DCS机制优化**：改进了待机重启问题的处理逻辑

#### 📋 脚本系统增强
- 🌐 **语言检测**：初始化脚本新增自动语言检测功能
- 📝 **日志增强**：所有日志输出支持中英文双语显示

---

