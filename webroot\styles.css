:root {
    /* 浅色模式变量 */
    --background-color: #f5f5f5;
    --card-background: #ffffff;
    --text-color: #333333;
    --secondary-text-color: #666666;
    --border-color: #e0e0e0;
    --accent-color: #0d84ff;
    --accent-hover: #0a6edb;
    --accent-bg: rgba(13, 132, 255, 0.1); /* 浅色模式下的强调背景色 */
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --error-color: #f44336;
    --header-background: #ffffff;
    --header-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    --card-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    --switch-background: #e0e0e0;
}

[data-theme="dark"] {
    /* 深色模式变量 */
    --background-color: #121212;
    --card-background: #1e1e1e;
    --text-color: #e0e0e0;
    --secondary-text-color: #aaaaaa;
    --border-color: #333333;
    --accent-color: #0d84ff;
    --accent-hover: #3a9bff;
    --accent-bg: rgba(13, 132, 255, 0.2); /* 深色模式下的强调背景色 */
    --success-color: #66bb6a;
    --warning-color: #ffa726;
    --error-color: #ef5350;
    --header-background: #1e1e1e;
    --header-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    --card-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    --switch-background: #333333;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.6;
    transition: background-color 0.3s, color 0.3s;
    padding-bottom: 90px; /* 增加底部空间，适应更高的导航栏 */
}

#loading {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 18px;
    font-weight: 500;
}

#loading::after {
    content: '.';
    animation: ellipsis 1.5s infinite;
}

@keyframes ellipsis {
    0% { content: '.'; }
    33% { content: '..'; }
    66% { content: '...'; }
    100% { content: '.'; }
}

.app-header {
    background-color: var(--header-background);
    box-shadow: var(--header-shadow);
    padding: 16px 0;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-content h1 {
    font-size: 20px;
    font-weight: 500;
    color: #0d84ff; /* 蓝色标题 */
}

.theme-toggle {
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: background-color 0.3s;
}

.theme-toggle:hover {
    background-color: var(--border-color);
}

.container {
    max-width: 800px;
    margin: 24px auto;
    padding: 0 16px;
}

.card {
    background-color: var(--card-background);
    border-radius: 12px;
    box-shadow: var(--card-shadow);
    margin-bottom: 24px;
    overflow: visible; /* 修改为visible，确保弹出内容不会被截断 */
}

.card-title {
    font-size: 16px;
    font-weight: 500;
    padding: 16px;
    border-bottom: 1px solid var(--border-color);
}

.card-content {
    padding: 16px;
    overflow: visible; /* 确保弹出内容不会被截断 */
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    position: relative; /* 确保子元素的绝对定位是相对于它 */
    overflow: visible; /* 确保弹出内容不会被截断 */
}

/* 设置页面中的选项间距更大 */
#settingsCard .status-item {
    margin-bottom: 24px; /* 增加间距 */
    padding-bottom: 8px; /* 添加底部内边距 */
}

.status-item:last-child {
    margin-bottom: 0;
}

.setting-description {
    margin-top: 8px;
    margin-bottom: 16px; /* 添加底部间距 */
    color: var(--secondary-text-color);
    font-size: 12px;
}

/* 设置页面中的描述文本 */
#settingsCard .setting-description {
    margin-bottom: 24px; /* 增加底部间距 */
}

.select-container {
    min-width: 150px;
    position: relative; /* 确保子元素的绝对定位是相对于它 */
    overflow: visible; /* 确保弹出内容不会被截断 */
}

.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
}

.status-running {
    background-color: var(--success-color);
    color: white;
}

.status-stopped {
    background-color: var(--error-color);
    color: white;
}

.version-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    background-color: var(--accent-color);
    color: white;
}

/* 开关样式 */
.switch {
    position: relative;
    display: inline-block;
    width: 48px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--switch-background);
    transition: .4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 20px;
    width: 20px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: .4s;
}

input:checked + .slider {
    background-color: var(--accent-color);
}

input:focus + .slider {
    box-shadow: 0 0 1px var(--accent-color);
}

input:checked + .slider:before {
    transform: translateX(24px);
}

.slider.round {
    border-radius: 24px;
}

.slider.round:before {
    border-radius: 50%;
}

/* 表格样式 */
.table-container {
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
}

th, td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

th {
    font-weight: 500;
    color: var(--secondary-text-color);
}

/* 游戏列表样式 */
.games-list {
    list-style: none;
    max-height: 200px;
    overflow-y: auto;
}

.games-list li {
    padding: 8px 0;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.games-list li:last-child {
    border-bottom: none;
}

.games-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 16px;
}

.game-delete-btn {
    background: none;
    border: none;
    cursor: pointer;
    color: var(--error-color);
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.3s;
}

.game-delete-btn:hover {
    background-color: var(--border-color);
}

/* 日志样式 */
.log-actions {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    align-items: center;
}

.log-actions .select-container {
    min-width: 120px;
}

.btn {
    background-color: var(--accent-color);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;
}

.btn:hover {
    background-color: var(--accent-hover);
}

.select {
    background-color: var(--card-background);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 8px;
    font-size: 14px;
}

/* 自定义下拉菜单样式 */
.custom-select {
    position: relative;
    width: 100%;
    cursor: pointer;
    z-index: 10; /* 默认z-index */
}

/* 为语言选择器设置更高的z-index */
#languageContainer {
    z-index: 30;
}

/* 为日志等级选择器设置较低的z-index */
#logLevelContainer {
    z-index: 20;
}

.selected-option {
    background-color: transparent;
    color: var(--text-color);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 14px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s;
}

/* 鼠标悬停时的样式 */
.selected-option:hover {
    border-color: var(--accent-color);
}

/* 打开状态时的样式 */
.custom-select.open .selected-option {
    background-color: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
}

.selected-option::after {
    content: "▼";
    font-size: 10px;
    margin-left: 8px;
    transition: transform 0.3s;
    color: var(--secondary-text-color);
}

.custom-select.open .selected-option::after {
    transform: rotate(180deg);
    color: white;
}

.options-container {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    margin-top: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    z-index: 11; /* 默认比选择器高1 */
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    opacity: 0;
}

/* 为语言选择器的选项容器设置更高的z-index */
#languageOptions {
    z-index: 31;
}

/* 为日志等级选择器的选项容器设置较低的z-index */
#logLevelOptions {
    z-index: 21;
}

.custom-select.open .options-container {
    max-height: 200px;
    overflow: visible; /* 确保内容不会被截断 */
    opacity: 1;
}

.option {
    padding: 8px 12px;
    font-size: 14px;
    color: var(--text-color);
    transition: background-color 0.2s;
    border-bottom: 1px solid var(--border-color);
}

.option:last-child {
    border-bottom: none;
}

.option:hover {
    background-color: var(--border-color);
}

.option.selected {
    background-color: var(--accent-color);
    color: white;
}

/* 数值选择器样式 */
.number-spinner {
    display: flex;
    align-items: center;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    overflow: hidden;
    background-color: var(--card-background);
}

.spinner-btn {
    background-color: var(--accent-color);
    color: white;
    border: none;
    width: 40px;
    height: 38px;
    font-size: 18px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s, transform 0.1s;
    user-select: none;
    -webkit-user-select: none;
    -webkit-tap-highlight-color: transparent;
}

.spinner-btn:hover {
    background-color: var(--accent-hover);
}

.spinner-btn:active {
    background-color: var(--accent-hover);
    transform: scale(0.95);
}

.spinner-btn:disabled {
    background-color: var(--border-color);
    cursor: not-allowed;
    opacity: 0.7;
    transform: none;
}

.spinner-value {
    flex: 1;
    text-align: center;
    padding: 8px 12px;
    font-size: 14px;
    color: var(--text-color);
    background-color: var(--background-color);
    border-left: 1px solid var(--border-color);
    border-right: 1px solid var(--border-color);
}

.log-content {
    background-color: var(--background-color);
    border-radius: 4px;
    padding: 12px;
    font-family: monospace;
    font-size: 12px;
    white-space: pre-wrap;
    max-height: 500px;
    overflow-y: auto;
}

.loading-text {
    text-align: center;
    color: var(--secondary-text-color);
    padding: 12px;
}

/* 页面容器样式 */
.page-container {
    position: relative;
    min-height: calc(100vh - 200px);
    overflow: visible; /* 确保弹出内容不会被截断 */
}

.page {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
    overflow: visible; /* 确保弹出内容不会被截断 */
}

.page.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* 底部导航栏样式 */
.nav-bar {
    display: flex;
    justify-content: space-around;
    align-items: flex-start; /* 改为顶部对齐 */
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 80px; /* 进一步增加底栏高度 */
    background-color: var(--card-background);
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    z-index: 90;
    border-top: 1px solid var(--border-color);
    padding-top: 10px; /* 顶部内边距，使内容整体上移 */
    -webkit-tap-highlight-color: transparent; /* 移除点击时的高亮效果 */
    user-select: none; /* 防止文本被选中 */
}

.nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start; /* 从顶部开始布局 */
    width: 25%;
    height: 60px; /* 限制高度 */
    background: none;
    border: none;
    color: var(--secondary-text-color);
    cursor: pointer;
    transition: all 0.3s;
    padding: 0;
    position: relative;
    overflow: visible; /* 确保内容不被截断 */
    -webkit-tap-highlight-color: transparent; /* 移除点击时的高亮效果 */
}

.nav-item.active {
    color: var(--accent-color);
}

.nav-item.active::before {
    content: "";
    position: absolute;
    top: 0;
    width: 70%;
    height: 42px; /* 增加高度以容纳文字和图标 */
    background-color: var(--accent-bg);
    border-radius: 18px;
    z-index: -1;
    transition: all 0.3s;
}

.nav-icon {
    font-size: 20px;
    margin-bottom: 4px;
    transition: transform 0.3s;
    margin-top: 0; /* 移除顶部边距 */
}

.nav-text {
    font-size: 12px;
    height: 0;
    opacity: 0;
    overflow: hidden;
    transition: all 0.3s;
    margin-top: 4px; /* 增加与图标的间距 */
}

.nav-item.active .nav-icon {
    transform: translateY(-4px);
}

.nav-item.active .nav-text {
    height: 16px;
    opacity: 1;
}

/* 版权信息样式 */
#copyrightCard {
    margin-top: 24px;
}

.copyright-content {
    text-align: center;
    padding: 12px !important;
    color: var(--secondary-text-color);
    font-size: 12px;
}

/* 配置编辑相关样式 */
.config-actions, .margin-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 16px;
}

.edit-btn, .delete-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background-color 0.3s;
}

.edit-btn {
    color: var(--accent-color);
}

.delete-btn {
    color: var(--error-color);
}

.edit-btn:hover, .delete-btn:hover {
    background-color: var(--border-color);
}

/* 模态对话框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 2000; /* 增加z-index确保在最上层 */
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.4);
}

.modal-content {
    background-color: var(--card-background);
    margin: 15% auto;
    padding: 20px;
    border-radius: 12px;
    box-shadow: var(--card-shadow);
    width: 80%;
    max-width: 500px;
    position: relative;
    z-index: 2001; /* 确保内容在模态框背景之上 */
}

.close-modal {
    color: var(--secondary-text-color);
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close-modal:hover {
    color: var(--text-color);
}

.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.form-group input, .form-group select {
    width: 100%;
    padding: 8px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: var(--background-color);
    color: var(--text-color);
}

.input-hint {
    display: block;
    margin-top: 4px;
    font-size: 12px;
    color: var(--secondary-text-color);
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    margin-top: 16px;
}

.btn-secondary {
    background-color: var(--border-color);
    color: var(--text-color);
}

.btn-secondary:hover {
    background-color: var(--secondary-text-color);
    color: var(--card-background);
}

.btn-danger {
    background-color: #e74c3c;
    color: white;
    margin-left: auto; /* 将删除按钮推到右侧 */
}

.btn-danger:hover {
    background-color: #c0392b;
}
